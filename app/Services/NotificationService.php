<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\CustomerUser;
use App\Notifications\Auth\AuthEmailChangedNotification;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use App\Notifications\Auth\AuthPasswordChangedNotification;
use App\Notifications\Customer\CustomerEmailChangedNotification;
use Illuminate\Support\Facades\Notification;

/**
 * Centralized service for managing notifications across the application.
 *
 * This service provides a consistent interface for sending notifications
 * and ensures proper naming conventions and consistency.
 * All notifications are now sent via Resend mailer for better deliverability.
 */
class NotificationService
{
    protected ResendMailService $resendMailService;

    public function __construct(ResendMailService $resendMailService)
    {
        $this->resendMailService = $resendMailService;
    }
    /**
     * Send email changed notification to Customer<PERSON>ser's old email.
     */
    public function sendUserEmailChangedNotification(CustomerUser $user, string $oldEmail): void
    {
        Notification::route('mail', $oldEmail)
            ->notify(new AuthEmailChangedNotification($user, $oldEmail));
    }

    /**
     * Send password changed notification to CustomerUser.
     */
    public function sendUserPasswordChangedNotification(CustomerUser $user): void
    {
        $user->notify(new AuthPasswordChangedNotification($user));
    }

    /**
     * Send email verified notification to CustomerUser.
     */
    public function sendUserEmailVerifiedNotification(CustomerUser $user): void
    {
        $user->notify(new AuthEmailVerifiedNotification($user));
    }

    /**
     * Send email changed notification to Customer's old email.
     */
    public function sendCustomerEmailChangedNotification(Customer $customer, string $oldEmail): void
    {
        Notification::route('mail', $oldEmail)
            ->notify(new CustomerEmailChangedNotification($customer, $oldEmail));
    }

    /**
     * Send email verified notification to Customer.
     */
    public function sendCustomerEmailVerifiedNotification(Customer $customer): void
    {
        $customer->notify(new AuthEmailVerifiedNotification($customer));
    }

    /**
     * Get all available notification types with their descriptions.
     * 
     * @return array<string, array{class: string, description: string, target: string}>
     */
    public function getAvailableNotifications(): array
    {
        return [
            'user_email_changed' => [
                'class' => AuthEmailChangedNotification::class,
                'description' => 'Sent to CustomerUser when email is changed',
                'target' => 'old_email',
            ],
            'user_password_changed' => [
                'class' => AuthPasswordChangedNotification::class,
                'description' => 'Sent to CustomerUser when password is changed',
                'target' => 'user',
            ],
            'user_email_verified' => [
                'class' => AuthEmailVerifiedNotification::class,
                'description' => 'Sent to CustomerUser when email is verified',
                'target' => 'user',
            ],
            'customer_email_changed' => [
                'class' => CustomerEmailChangedNotification::class,
                'description' => 'Sent to Customer when business email is changed',
                'target' => 'old_email',
            ],
            'customer_email_verified' => [
                'class' => AuthEmailVerifiedNotification::class,
                'description' => 'Sent to Customer when business email is verified',
                'target' => 'customer',
            ],
        ];
    }

    /**
     * Validate notification consistency across the application.
     * 
     * @return array<string, mixed>
     */
    public function validateNotificationConsistency(): array
    {
        $notifications = $this->getAvailableNotifications();
        $issues = [];

        foreach ($notifications as $key => $config) {
            $class = $config['class'];
            
            // Check if class exists
            if (!class_exists($class)) {
                $issues[] = "Notification class {$class} does not exist";
                continue;
            }

            // Check if implements ShouldQueue
            $reflection = new \ReflectionClass($class);
            $interfaces = $reflection->getInterfaceNames();
            
            if (!in_array('Illuminate\Contracts\Queue\ShouldQueue', $interfaces)) {
                $issues[] = "Notification {$class} should implement ShouldQueue";
            }

            // Check naming convention
            $className = class_basename($class);
            if (str_starts_with($key, 'user_') && !str_starts_with($className, 'Auth')) {
                $issues[] = "User notification {$class} should start with 'Auth' prefix";
            }
            
            if (str_starts_with($key, 'customer_') && !str_starts_with($className, 'Customer') && !str_starts_with($className, 'Auth')) {
                $issues[] = "Customer notification {$class} should start with 'Customer' or 'Auth' prefix";
            }
        }

        return [
            'total_notifications' => count($notifications),
            'issues' => $issues,
            'is_consistent' => empty($issues),
        ];
    }
}
