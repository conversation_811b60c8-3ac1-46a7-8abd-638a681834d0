<?php

namespace App\Services;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/**
 * Service for handling email sending via Resend mailer.
 * This service provides a centralized way to send emails using Resend.
 */
class ResendMailService
{
    /**
     * Send notification via Resend mailer.
     */
    public function sendNotification(object $notifiable, Notification $notification): bool
    {
        try {
            if (! method_exists($notification, 'toMail')) {
                Log::warning('Notification does not have toMail method', [
                    'notification' => get_class($notification),
                    'notifiable' => get_class($notifiable),
                ]);
                return false;
            }

            $message = $notification->toMail($notifiable);
            
            if (! $message) {
                Log::warning('Notification toMail returned null', [
                    'notification' => get_class($notification),
                    'notifiable' => get_class($notifiable),
                ]);
                return false;
            }

            $to = $notifiable->routeNotificationFor('mail', $notification);
            
            if (! $to) {
                Log::warning('No email address found for notifiable', [
                    'notification' => get_class($notification),
                    'notifiable' => get_class($notifiable),
                ]);
                return false;
            }

            // Send via Resend mailer
            Mail::mailer('resend')->send([], [], function ($mail) use ($message, $to, $notifiable, $notification) {
                $mail->to($to);
                
                // Set subject
                if (method_exists($message, 'subject') && $message->subject) {
                    $mail->subject($message->subject);
                } elseif (property_exists($message, 'subject') && $message->subject) {
                    $mail->subject($message->subject);
                }
                
                // Set view or markdown
                if (method_exists($message, 'markdown') && $message->markdown) {
                    $viewData = method_exists($message, 'viewData') ? $message->viewData : [];
                    if (property_exists($message, 'viewData')) {
                        $viewData = $message->viewData;
                    }
                    $mail->markdown($message->markdown, $viewData);
                } elseif (method_exists($message, 'view') && $message->view) {
                    $viewData = method_exists($message, 'viewData') ? $message->viewData : [];
                    if (property_exists($message, 'viewData')) {
                        $viewData = $message->viewData;
                    }
                    $mail->view($message->view, $viewData);
                }
                
                // Set from address
                if (method_exists($message, 'from') && $message->from) {
                    $from = is_array($message->from) ? $message->from[0] : $message->from;
                    $mail->from($from['address'] ?? $from, $from['name'] ?? null);
                } elseif (property_exists($message, 'from') && $message->from) {
                    $from = is_array($message->from) ? $message->from[0] : $message->from;
                    $mail->from($from['address'] ?? $from, $from['name'] ?? null);
                }
            });

            Log::info('Email sent successfully via Resend', [
                'notification' => get_class($notification),
                'notifiable' => get_class($notifiable),
                'to' => $to,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send email via Resend', [
                'notification' => get_class($notification),
                'notifiable' => get_class($notifiable),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send email directly via Resend mailer.
     */
    public function sendMail(string $to, string $subject, string $view, array $data = []): bool
    {
        try {
            Mail::mailer('resend')->send($view, $data, function ($mail) use ($to, $subject) {
                $mail->to($to)->subject($subject);
            });

            Log::info('Direct email sent successfully via Resend', [
                'to' => $to,
                'subject' => $subject,
                'view' => $view,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send direct email via Resend', [
                'to' => $to,
                'subject' => $subject,
                'view' => $view,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Send markdown email via Resend mailer.
     */
    public function sendMarkdownMail(string $to, string $subject, string $markdown, array $data = []): bool
    {
        try {
            Mail::mailer('resend')->send([], [], function ($mail) use ($to, $subject, $markdown, $data) {
                $mail->to($to)
                     ->subject($subject)
                     ->markdown($markdown, $data);
            });

            Log::info('Markdown email sent successfully via Resend', [
                'to' => $to,
                'subject' => $subject,
                'markdown' => $markdown,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to send markdown email via Resend', [
                'to' => $to,
                'subject' => $subject,
                'markdown' => $markdown,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
