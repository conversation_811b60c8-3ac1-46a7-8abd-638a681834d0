<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\CustomerUser;
use App\Services\ResendMailService;
use Illuminate\Console\Command;

class TestResendEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:resend-email 
                            {--type=customer : Type of user (customer or customer_user)}
                            {--email= : Email address to test with}
                            {--id= : ID of the user to test with}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending email via Resend service';

    /**
     * Execute the console command.
     */
    public function handle(ResendMailService $resendService): int
    {
        $type = $this->option('type');
        $email = $this->option('email');
        $id = $this->option('id');

        $this->info('Testing email service...');
        $this->info('Current mail driver: ' . config('mail.default'));

        try {
            if ($type === 'customer') {
                $user = $this->getCustomer($email, $id);
                if (!$user) {
                    $this->error('Customer not found');
                    return 1;
                }
                
                $this->info("Testing with Customer: {$user->business_name} ({$user->email})");

                // Test email verification notification using standard method
                $this->info('Sending email verification notification...');
                $user->sendEmailVerificationNotification();
                $this->info('✅ Email verification notification sent!');

                // If using resend, also test direct service
                if (config('mail.default') === 'resend') {
                    $this->info('Testing direct Resend service...');
                    $notification = new \App\Notifications\Customer\CustomerEmailVerificationNotification();
                    $result = $resendService->sendNotification($user, $notification);

                    if ($result) {
                        $this->info('✅ Direct Resend service test passed!');
                    } else {
                        $this->warn('⚠️ Direct Resend service test failed');
                    }
                }
                
            } else {
                $user = $this->getCustomerUser($email, $id);
                if (!$user) {
                    $this->error('CustomerUser not found');
                    return 1;
                }

                $this->info("Testing with CustomerUser: {$user->name} ({$user->email})");

                // Test email verification notification using standard method
                $this->info('Sending email verification notification...');
                $user->sendEmailVerificationNotification();
                $this->info('✅ Email verification notification sent!');

                // If using resend, also test direct service
                if (config('mail.default') === 'resend') {
                    $this->info('Testing direct Resend service...');
                    $notification = new \App\Notifications\VerifyEmailNotification();
                    $result = $resendService->sendNotification($user, $notification);

                    if ($result) {
                        $this->info('✅ Direct Resend service test passed!');
                    } else {
                        $this->warn('⚠️ Direct Resend service test failed');
                    }
                }
            }

            // Test direct markdown email if using resend
            if (config('mail.default') === 'resend') {
                $this->info('Testing direct markdown email via Resend...');
                $directResult = $resendService->sendMarkdownMail(
                    $user->email,
                    'Test Email from Resend',
                    'emails.test-resend',
                    ['user' => $user, 'timestamp' => now()]
                );

                if ($directResult) {
                    $this->info('✅ Direct markdown email sent successfully!');
                } else {
                    $this->warn('⚠️ Direct markdown email failed');
                }
            } else {
                $this->info('Skipping direct Resend tests (not using resend driver)');
            }

        } catch (\Exception $e) {
            $this->error("Error: {$e->getMessage()}");
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            return 1;
        }

        return 0;
    }

    private function getCustomer(?string $email, ?string $id): ?Customer
    {
        if ($id) {
            return Customer::find($id);
        }
        
        if ($email) {
            return Customer::where('email', $email)->first();
        }
        
        return Customer::first();
    }

    private function getCustomerUser(?string $email, ?string $id): ?CustomerUser
    {
        if ($id) {
            return CustomerUser::find($id);
        }
        
        if ($email) {
            return CustomerUser::where('email', $email)->first();
        }
        
        return CustomerUser::first();
    }
}
