<?php

namespace App\Console\Commands;

use App\Notifications\TestResendNotification;
use Illuminate\Console\Command;

class TestSimpleResendCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:simple-resend {--email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test simple resend integration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $email = $this->option('email');
        
        $this->info('Testing simple resend integration...');
        $this->info('Current mail driver: ' . config('mail.default'));
        $this->info('Testing with email: ' . $email);

        try {
            // Create a mock notifiable
            $notifiable = new class($email) {
                use \Illuminate\Notifications\Notifiable;
                
                public function __construct(public string $email) {}
                
                public function routeNotificationFor($channel, $notification = null)
                {
                    return $this->email;
                }
                
                public function getKey()
                {
                    return 1;
                }
                
                public function getEmailForVerification()
                {
                    return $this->email;
                }
            };

            // Send notification
            $notification = new TestResendNotification();
            $notifiable->notify($notification);
            
            $this->info('✅ Notification sent successfully!');
            
            if (config('mail.default') === 'resend') {
                $this->info('📧 Used Resend mail service');
            } else {
                $this->info('📧 Used standard mail driver: ' . config('mail.default'));
            }
            
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            return 1;
        }
    }
}
