<?php

namespace App\Console\Commands;

use App\Channels\ResendMailChannel;
use App\Notifications\Customer\CustomerEmailVerificationNotification;
use Illuminate\Console\Command;
use Illuminate\Notifications\Messages\MailMessage;

class TestMailChannelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mail-channel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test mail channel functionality without database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing mail channel functionality...');
        $this->info('Current mail driver: ' . config('mail.default'));

        try {
            // Create a mock notifiable
            $notifiable = new class {
                use \Illuminate\Notifications\Notifiable;

                public $email = '<EMAIL>';

                public function routeNotificationFor($channel, $notification = null)
                {
                    return $this->email;
                }
            };

            // Create a test notification
            $notification = new class extends \Illuminate\Notifications\Notification {
                public function via($notifiable)
                {
                    return ['mail'];
                }
                
                public function toMail($notifiable)
                {
                    return (new MailMessage)
                        ->subject('Test Email')
                        ->line('This is a test email.')
                        ->line('Testing mail channel functionality.');
                }
            };

            // Test the channel
            if (config('mail.default') === 'resend') {
                $this->info('Testing ResendMailChannel...');
                $channel = new ResendMailChannel();
                $channel->send($notifiable, $notification);
                $this->info('✅ ResendMailChannel test completed!');
            } else {
                $this->info('Using standard mail channel (not resend)');
                // Test standard notification
                $notifiable->notify($notification);
                $this->info('✅ Standard mail channel test completed!');
            }

            $this->info('🎉 All tests passed!');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            
            if ($this->option('verbose')) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
            
            return 1;
        }
    }
}
