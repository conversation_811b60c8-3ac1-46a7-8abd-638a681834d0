<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class ValidateNotificationConsistencyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'validate:notifications 
                            {--fix : Attempt to fix issues automatically}
                            {--verbose : Show detailed information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate notification consistency across the application';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService)
    {
        $this->info('🔍 Validating notification consistency...');
        $this->newLine();

        $result = $notificationService->validateNotificationConsistency();

        // Display summary
        $this->info("📊 Summary:");
        $this->line("   Total notifications: {$result['total_notifications']}");
        
        if ($result['is_consistent']) {
            $this->info("   ✅ All notifications are consistent!");
        } else {
            $this->error("   ❌ Found {" . count($result['issues']) . "} consistency issues");
        }

        $this->newLine();

        // Display issues if any
        if (!empty($result['issues'])) {
            $this->error('🚨 Issues found:');
            foreach ($result['issues'] as $issue) {
                $this->line("   • {$issue}");
            }
            $this->newLine();
        }

        // Show detailed information if verbose
        if ($this->option('verbose')) {
            $this->displayDetailedInformation($notificationService);
        }

        // Show recommendations
        $this->displayRecommendations($result);

        return $result['is_consistent'] ? 0 : 1;
    }

    /**
     * Display detailed information about notifications.
     */
    protected function displayDetailedInformation(NotificationService $notificationService): void
    {
        $this->info('📋 Detailed Information:');
        
        $notifications = $notificationService->getAvailableNotifications();
        
        foreach ($notifications as $key => $config) {
            $this->line("   🔔 {$key}:");
            $this->line("      Class: {$config['class']}");
            $this->line("      Description: {$config['description']}");
            $this->line("      Target: {$config['target']}");
            
            // Check if class exists and get additional info
            if (class_exists($config['class'])) {
                $reflection = new \ReflectionClass($config['class']);
                $interfaces = $reflection->getInterfaceNames();
                
                $this->line("      Implements ShouldQueue: " . 
                    (in_array('Illuminate\Contracts\Queue\ShouldQueue', $interfaces) ? '✅' : '❌'));
            } else {
                $this->line("      Status: ❌ Class not found");
            }
            
            $this->newLine();
        }
    }

    /**
     * Display recommendations for improving notification consistency.
     */
    protected function displayRecommendations(array $result): void
    {
        $this->info('💡 Recommendations:');
        
        if ($result['is_consistent']) {
            $this->line('   • Your notifications are well-structured! Consider:');
            $this->line('     - Adding comprehensive tests for all notification scenarios');
            $this->line('     - Documenting notification flows for team members');
            $this->line('     - Setting up monitoring for notification delivery rates');
        } else {
            $this->line('   • Fix the consistency issues listed above');
            $this->line('   • Ensure all notifications implement ShouldQueue for better performance');
            $this->line('   • Follow naming conventions: Auth* for user notifications, Customer* for business notifications');
            $this->line('   • Add comprehensive tests for all notification types');
        }

        $this->newLine();
        $this->line('   • Run tests: php artisan test tests/Feature/NotificationTest.php');
        $this->line('   • Run tests: php artisan test tests/Unit/NotificationConsistencyTest.php');
        $this->line('   • Test notifications: php artisan test:notifications');
    }
}
