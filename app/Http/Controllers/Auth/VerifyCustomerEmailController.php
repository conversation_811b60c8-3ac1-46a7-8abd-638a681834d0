<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\VerifyCustomerEmailRequest;
use App\Models\Customer;
use Illuminate\Auth\Events\Verified;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class VerifyCustomerEmailController extends Controller
{
    /**
     * Mark the customer's email address as verified.
     */
    public function __invoke(Request $request, int $id, string $hash): RedirectResponse
    {
        try {
            /** @var Customer $customer */
            $customer = Customer::findOrFail($id);

            // Verify the hash matches the customer's email
            if (! hash_equals($hash, sha1($customer->getEmailForVerification()))) {
                Log::warning('Customer email verification failed: Invalid hash', [
                    'customer_id' => $id,
                    'email' => $customer->email,
                    'provided_hash' => $hash,
                ]);

                return redirect()->route('home')
                    ->with('error', __('messages.email.verification_invalid'));
            }

            // Check if already verified
            if ($customer->hasVerifiedEmail()) {
                Log::info('Customer email already verified', [
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                ]);

                return redirect()->route('home')
                    ->with('status', __('messages.email.customer_already_verified'));
            }

            // Mark as verified
            if ($customer->markEmailAsVerified()) {
                event(new Verified($customer));

                Log::info('Customer email verified successfully', [
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                    'verified_at' => now(),
                ]);
            }

            return redirect()->route('home')
                ->with('success', __('messages.email.customer_verified_success'));

        } catch (ModelNotFoundException $e) {
            Log::warning('Customer email verification failed: Customer not found', [
                'customer_id' => $id,
                'hash' => $hash,
            ]);

            return redirect()->route('home')
                ->with('error', __('messages.email.customer_not_found'));

        } catch (\InvalidArgumentException $e) {
            Log::warning('Customer email verification failed: Invalid arguments', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('home')
                ->with('error', __('messages.email.verification_invalid'));

        } catch (\Exception $e) {
            Log::error('Customer email verification error: Unexpected exception', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return redirect()->route('home')
                ->with('error', __('messages.email.verification_failed'));
        }
    }

    /**
     * Resend the email verification notification for customer.
     */
    public function resend(VerifyCustomerEmailRequest $request): RedirectResponse|JsonResponse
    {
        try {
            /** @var Customer $customer */
            $customer = Customer::where('email', $request->email)->firstOrFail();

            if ($customer->hasVerifiedEmail()) {
                return back()->with('status', __('messages.email.customer_already_verified'));
            }

            // Try to send via Resend first, fallback to regular notification
            if (method_exists($customer, 'sendEmailVerificationNotificationViaResend')) {
                $sent = $customer->sendEmailVerificationNotificationViaResend();
                if (!$sent) {
                    // Fallback to regular notification
                    $customer->sendEmailVerificationNotification();
                }
            } else {
                $customer->sendEmailVerificationNotification();
            }

            Log::info('Customer email verification resent', [
                'customer_id' => $customer->id,
                'email' => $customer->email,
            ]);

            if ($request->has('is_api') && $request->is_api) {
                return $this->success([], __('messages.email.customer_verification_resent'));
            }

            return back()->with('status', __('messages.email.customer_verification_resent'));

        } catch (\Exception $e) {
            Log::error('Customer email verification resend error', [
                'email' => $request->email,
                'error' => $e->getMessage(),
            ]);

            if ($request->has('is_api') && $request->is_api) {
                return $this->error([], __('messages.email.verification_resend_failed'));
            }

            return back()->with('error', __('messages.email.verification_resend_failed'));
        }
    }
}
