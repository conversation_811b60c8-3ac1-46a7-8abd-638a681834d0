<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class EmailVerificationController extends Controller
{
    /**
     * Resend the email verification notification.
     */
    public function resend(Request $request): RedirectResponse
    {
        $user = $request->user('customer_user');

        // Try to send via Resend first, fallback to regular notification
        if (method_exists($user, 'sendEmailVerificationNotificationViaResend')) {
            $sent = $user->sendEmailVerificationNotificationViaResend();
            if (!$sent) {
                // Fallback to regular notification
                $user->sendEmailVerificationNotification();
            }
        } else {
            $user->sendEmailVerificationNotification();
        }

        return back()->with('status', __('messages.email.verification_resent'));
    }
}
