<?php

namespace App\Http\Middleware;

use App\Http\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class TemplateFavoriteRateLimit
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $this->resolveRequestSignature($request);

        // Get rate limiting configuration
        $config = config('rate_limiting.template_favorites');
        $maxAttempts = $config['max_attempts'];
        $decayMinutes = $config['decay_minutes'];

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);

            return $this->tooManyRequests(
                message: __('messages.errors.too_many_requests', ['seconds' => $seconds]),
                retryAfter: $seconds
            );
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        return $next($request);
    }

    /**
     * Resolve the rate limiting signature for the request.
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $userId = auth('customer_user')->id();
        $ip = $request->ip();
        $keyPrefix = config('rate_limiting.template_favorites.key_prefix');

        // Use user ID if authenticated, otherwise use IP
        $identifier = $userId ? "user:{$userId}" : "ip:{$ip}";

        return "{$keyPrefix}:{$identifier}";
    }
}
