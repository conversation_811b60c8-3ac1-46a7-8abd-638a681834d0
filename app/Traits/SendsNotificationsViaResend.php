<?php

namespace App\Traits;

use App\Services\ResendMailService;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\App;

/**
 * Trait for models that need to send notifications via Resend.
 * This trait provides methods to send notifications using the Resend mailer.
 */
trait SendsNotificationsViaResend
{
    /**
     * Send a notification via Resend mailer.
     */
    public function notifyViaResend(Notification $notification): bool
    {
        $resendService = App::make(ResendMailService::class);
        return $resendService->sendNotification($this, $notification);
    }

    /**
     * Send email verification notification via Resend.
     * Override the default method to use Resend.
     */
    public function sendEmailVerificationNotificationViaResend(): bool
    {
        if (method_exists($this, 'sendEmailVerificationNotification')) {
            // Get the notification that would be sent
            $reflection = new \ReflectionMethod($this, 'sendEmailVerificationNotification');
            $reflection->setAccessible(true);
            
            // Create the notification manually based on the model type
            if ($this instanceof \App\Models\Customer) {
                $notification = new \App\Notifications\Customer\CustomerEmailVerificationNotification();
            } elseif ($this instanceof \App\Models\CustomerUser) {
                $notification = new \App\Notifications\VerifyEmailNotification();
            } else {
                return false;
            }
            
            return $this->notifyViaResend($notification);
        }
        
        return false;
    }

    /**
     * Send password reset notification via Resend.
     */
    public function sendPasswordResetNotificationViaResend(string $token): bool
    {
        $notification = new \App\Notifications\ResetPasswordNotification($token);
        return $this->notifyViaResend($notification);
    }

    /**
     * Route notifications for the mail channel to use email address.
     */
    public function routeNotificationForMail($notification = null): ?string
    {
        if (method_exists($this, 'getEmailForVerification')) {
            return $this->getEmailForVerification();
        }
        
        if (property_exists($this, 'email')) {
            return $this->email;
        }
        
        return null;
    }

    /**
     * Route notifications for the ResendMailChannel.
     */
    public function routeNotificationForResendMailChannel($notification = null): ?string
    {
        return $this->routeNotificationForMail($notification);
    }
}
