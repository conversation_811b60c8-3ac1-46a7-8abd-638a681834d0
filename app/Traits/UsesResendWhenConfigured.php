<?php

namespace App\Traits;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * Trait to handle sending emails via Resend when configured.
 * Simple approach: check config and use Mail::to() directly.
 */
trait UsesResendWhenConfigured
{
    /**
     * Override the notification sending to use Mail::to() when resend is configured.
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        // If MAIL_MAILER is set to 'resend' and properly configured, handle email sending directly
        if (config('mail.default') === 'resend' && $this->isResendConfigured()) {
            try {
                $this->sendViaResend($notifiable);
                return []; // Return empty array to prevent normal notification sending
            } catch (\Exception $e) {
                // If resend fails, fallback to normal mail channel
                Log::error('Resend failed in via(), falling back to mail channel', [
                    'error' => $e->getMessage(),
                    'notification' => get_class($this),
                ]);
                return ['mail'];
            }
        }

        // Otherwise, use normal mail channel
        return ['mail'];
    }

    /**
     * Send email directly via Resend using Mail::to().
     */
    protected function sendViaResend($notifiable)
    {
        try {
            $mailMessage = $this->toMail($notifiable);
            $to = $this->getRecipientEmail($notifiable);
            
            if (!$to) {
                return;
            }

            // Use Mail::to() with resend mailer
            Mail::mailer('resend')
                ->to($to)
                ->send(new \App\Mail\NotificationMail($mailMessage, $to));
                
        } catch (\Exception $e) {
            // Log error and fallback to normal notification
            Log::error('Resend email failed, falling back to normal notification', [
                'error' => $e->getMessage(),
                'notification' => get_class($this),
                'notifiable' => get_class($notifiable),
            ]);
            
            // Don't fallback here to avoid infinite loop
            // The via() method will return ['mail'] to use normal channel
        }
    }

    /**
     * Get recipient email address from notifiable.
     */
    protected function getRecipientEmail($notifiable): ?string
    {
        if (method_exists($notifiable, 'routeNotificationFor')) {
            return $notifiable->routeNotificationFor('mail');
        }
        
        if (method_exists($notifiable, 'getEmailForVerification')) {
            return $notifiable->getEmailForVerification();
        }
        
        if (property_exists($notifiable, 'email')) {
            return $notifiable->email;
        }
        
        return null;
    }

    /**
     * Check if Resend is properly configured.
     */
    protected function isResendConfigured(): bool
    {
        $apiKey = config('services.resend.key') ?: env('RESEND_API_KEY');
        return !empty($apiKey);
    }
}
