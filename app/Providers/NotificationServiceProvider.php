<?php

namespace App\Providers;

use App\Channels\ResendMailChannel;
use Illuminate\Notifications\ChannelManager;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;

class NotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register the custom ResendMailChannel
        Notification::resolved(function (ChannelManager $service) {
            $service->extend('resend_mail', function ($app) {
                return new ResendMailChannel();
            });
        });
    }
}
