<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;

/**
 * Simple wrapper to convert MailMessage to Mailable for use with Mail::to().
 */
class NotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public MailMessage $mailMessage,
        public string $recipient
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            to: $this->recipient,
            subject: $this->mailMessage->subject ?: 'Notification',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // If the MailMessage has a markdown template
        if ($this->mailMessage->markdown) {
            return new Content(
                markdown: $this->mailMessage->markdown,
                with: $this->mailMessage->viewData,
            );
        }
        
        // If the MailMessage has a view template
        if ($this->mailMessage->view) {
            return new Content(
                view: $this->mailMessage->view,
                with: $this->mailMessage->viewData,
            );
        }
        
        // Fallback: create simple HTML from MailMessage content
        return new Content(
            htmlString: $this->buildHtmlFromMailMessage(),
        );
    }

    /**
     * Build HTML content from MailMessage properties.
     */
    protected function buildHtmlFromMailMessage(): string
    {
        $html = '<html><body>';
        
        // Add greeting
        if ($this->mailMessage->greeting) {
            $html .= '<h2>' . $this->mailMessage->greeting . '</h2>';
        }
        
        // Add intro lines
        foreach ($this->mailMessage->introLines as $line) {
            $html .= '<p>' . $line . '</p>';
        }
        
        // Add action button
        if ($this->mailMessage->actionText && $this->mailMessage->actionUrl) {
            $html .= '<p><a href="' . $this->mailMessage->actionUrl . '" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">' . $this->mailMessage->actionText . '</a></p>';
        }
        
        // Add outro lines
        foreach ($this->mailMessage->outroLines as $line) {
            $html .= '<p>' . $line . '</p>';
        }
        
        // Add salutation
        if ($this->mailMessage->salutation) {
            $html .= '<p>' . $this->mailMessage->salutation . '</p>';
        }
        
        $html .= '</body></html>';
        
        return $html;
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
