<?php

namespace App\Notifications\Customer;

use App\Traits\ConfigurableMailChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class CustomerEmailVerificationNotification extends Notification implements ShouldQueue
{
    use ConfigurableMailChannel;
    use Queueable;

    /**
     * The callback that should be used to create the verify email URL.
     */
    public static ?\Closure $createUrlCallback = null;

    /**
     * The callback that should be used to build the mail message.
     */
    public static ?\Closure $toMailCallback = null;

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return $this->getMailChannels();
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $verificationUrl = $this->verificationUrl($notifiable);

        if (static::$toMailCallback) {
            return call_user_func(static::$toMailCallback, $notifiable, $verificationUrl);
        }

        return $this->buildMailMessage($verificationUrl);
    }

    /**
     * Get the verify email notification mail message for the given URL.
     */
    protected function buildMailMessage(string $url): MailMessage
    {
        return (new MailMessage)
            ->subject(__('messages.email.customer_verify_subject'))
            ->line(__('messages.email.customer_verify_greeting'))
            ->line(__('messages.email.customer_verify_message'))
            ->action(__('messages.email.customer_verify_button'), $url)
            ->line(__('messages.email.customer_verify_note'))
            ->line(__('messages.email.customer_verify_ignore'));
    }

    /**
     * Get the verification URL for the given notifiable.
     */
    protected function verificationUrl(object $notifiable): string
    {
        if (static::$createUrlCallback) {
            return call_user_func(static::$createUrlCallback, $notifiable);
        }

        return URL::temporarySignedRoute(
            'customer.verification.verify',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60)),
            [
                'id' => $notifiable->getKey(),
                'hash' => sha1($notifiable->getEmailForVerification()),
            ]
        );
    }

    /**
     * Set a callback that should be used when creating the email verification URL.
     */
    public static function createUrlUsing(\Closure $callback): void
    {
        static::$createUrlCallback = $callback;
    }

    /**
     * Set a callback that should be used when building the notification mail message.
     */
    public static function toMailUsing(\Closure $callback): void
    {
        static::$toMailCallback = $callback;
    }
}
