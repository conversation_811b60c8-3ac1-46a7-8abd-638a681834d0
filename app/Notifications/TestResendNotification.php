<?php

namespace App\Notifications;

use App\Traits\UsesResendWhenConfigured;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TestResendNotification extends Notification
{
    use UsesResendWhenConfigured;

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Test Resend Email')
            ->line('This is a test email to verify Resend integration.')
            ->line('If you receive this, the integration is working!')
            ->action('Visit Website', url('/'))
            ->line('Thank you for testing!');
    }
}
