<?php

namespace App\Notifications\Auth;

use App\Traits\UsesResendWhenConfigured;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AuthEmailVerifiedNotification extends Notification implements ShouldQueue
{
    use Queueable;
    use UsesResendWhenConfigured;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public object $user
    ) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject((string) __('messages.email.email_verified.title'))
            ->markdown('emails.auth.email-verified', [
                'user' => $this->user,
                'verifiedAt' => now()->format('d/m/Y H:i:s'),
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        /** @var \App\Models\User|\App\Models\CustomerUser $user */
        $user = $this->user;

        return [
            'user_id' => $user->id,
            'verified_at' => now(),
        ];
    }
}
