<?php

namespace App\Channels;

use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;

/**
 * Custom notification channel for sending emails via Resend mailer.
 * This channel ensures that all notifications use the Resend service.
 */
class ResendMailChannel
{
    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        if (! method_exists($notification, 'toMail')) {
            return;
        }

        $message = $notification->toMail($notifiable);

        // If the message is a Mailable, send it directly
        if ($message instanceof Mailable) {
            Mail::mailer('resend')->to($notifiable->routeNotificationFor('mail', $notification))->send($message);
            return;
        }

        // If it's a MailMessage, convert it to a mailable and send
        if (method_exists($message, 'toMail')) {
            $mailable = $message->toMail();
            Mail::mailer('resend')->to($notifiable->routeNotificationFor('mail', $notification))->send($mailable);
            return;
        }

        // For MailMessage objects, we need to handle them differently
        $to = $notifiable->routeNotificationFor('mail', $notification);

        if (! $to) {
            return;
        }

        // Build the mail using Laravel's mail system with Resend mailer
        Mail::mailer('resend')->send([], [], function ($mail) use ($message, $to) {
            $mail->to($to);

            // Handle subject
            if (property_exists($message, 'subject') && $message->subject) {
                $mail->subject($message->subject);
            } elseif (method_exists($message, 'subject') && $message->subject) {
                $mail->subject($message->subject);
            }

            // Handle view/markdown
            if (property_exists($message, 'markdown') && $message->markdown) {
                $viewData = property_exists($message, 'viewData') ? $message->viewData : [];
                $mail->markdown($message->markdown, $viewData);
            } elseif (property_exists($message, 'view') && $message->view) {
                $viewData = property_exists($message, 'viewData') ? $message->viewData : [];
                $mail->view($message->view, $viewData);
            } elseif (method_exists($message, 'markdown') && $message->markdown) {
                $viewData = method_exists($message, 'viewData') ? $message->viewData : [];
                $mail->markdown($message->markdown, $viewData);
            } elseif (method_exists($message, 'view') && $message->view) {
                $viewData = method_exists($message, 'viewData') ? $message->viewData : [];
                $mail->view($message->view, $viewData);
            }

            // Handle from address
            if (property_exists($message, 'from') && $message->from) {
                $from = is_array($message->from) ? $message->from[0] : $message->from;
                if (is_array($from)) {
                    $mail->from($from['address'], $from['name'] ?? null);
                }
            }

            // Handle reply-to
            if (property_exists($message, 'replyTo') && $message->replyTo) {
                foreach ($message->replyTo as $replyTo) {
                    $mail->replyTo($replyTo['address'], $replyTo['name'] ?? null);
                }
            }

            // Handle CC
            if (property_exists($message, 'cc') && $message->cc) {
                foreach ($message->cc as $cc) {
                    $mail->cc($cc['address'], $cc['name'] ?? null);
                }
            }

            // Handle BCC
            if (property_exists($message, 'bcc') && $message->bcc) {
                foreach ($message->bcc as $bcc) {
                    $mail->bcc($bcc['address'], $bcc['name'] ?? null);
                }
            }

            // Handle attachments
            if (property_exists($message, 'attachments') && $message->attachments) {
                foreach ($message->attachments as $attachment) {
                    $mail->attach($attachment['file'], $attachment['options'] ?? []);
                }
            }
        });
    }
}
