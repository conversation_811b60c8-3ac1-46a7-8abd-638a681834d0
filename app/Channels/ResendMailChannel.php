<?php

namespace App\Channels;

use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;

/**
 * Custom notification channel for sending emails via Resend mailer.
 * This channel ensures that all notifications use the Resend service.
 */
class ResendMailChannel
{
    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        // Check if Resend is properly configured
        if (! $this->isResendConfigured()) {
            \Log::info('Resend not configured, falling back to standard mail');
            // Fallback to standard mail channel
            $this->sendViaStandardMail($notifiable, $notification);
            return;
        }

        \Log::info('Using Resend mail channel');

        if (! method_exists($notification, 'toMail')) {
            return;
        }

        $message = $notification->toMail($notifiable);

        // If the message is a Mailable, send it directly
        if ($message instanceof Mailable) {
            try {
                Mail::mailer('resend')->to($notifiable->routeNotificationFor('mail', $notification))->send($message);
            } catch (\Exception $e) {
                // Fallback to standard mail
                $this->sendViaStandardMail($notifiable, $notification);
            }
            return;
        }

        if (! $message instanceof MailMessage) {
            return;
        }

        $to = $notifiable->routeNotificationFor('mail', $notification);

        if (! $to) {
            return;
        }

        try {
            // Ensure mail views are available
            $this->ensureMailViewsAreRegistered();

            // Create a simple mailable to avoid namespace issues
            $mailable = new class($message, $to) extends \Illuminate\Mail\Mailable {
                public function __construct(
                    private MailMessage $message,
                    private string $recipient
                ) {}

                public function build()
                {
                    $this->to($this->recipient)
                         ->subject($this->message->subject);

                    // Use a simple view instead of markdown to avoid namespace issues
                    if ($this->message->view) {
                        $this->view($this->message->view, $this->message->viewData);
                    } else {
                        // Create a simple HTML email
                        $html = '<html><body>';
                        $html .= '<h1>' . $this->message->subject . '</h1>';
                        foreach ($this->message->introLines as $line) {
                            $html .= '<p>' . $line . '</p>';
                        }
                        if ($this->message->actionText && $this->message->actionUrl) {
                            $html .= '<p><a href="' . $this->message->actionUrl . '">' . $this->message->actionText . '</a></p>';
                        }
                        foreach ($this->message->outroLines as $line) {
                            $html .= '<p>' . $line . '</p>';
                        }
                        $html .= '</body></html>';

                        $this->html($html);
                    }

                    return $this;
                }
            };

            Mail::mailer('resend')->send($mailable);

        } catch (\Exception $e) {
            // Fallback to standard mail
            $this->sendViaStandardMail($notifiable, $notification);
        }
    }

    /**
     * Check if Resend is properly configured.
     */
    private function isResendConfigured(): bool
    {
        if (config('mail.default') !== 'resend') {
            return false;
        }

        $apiKey = config('services.resend.key') ?: env('RESEND_API_KEY');
        return !empty($apiKey);
    }

    /**
     * Send notification via standard mail channel as fallback.
     */
    private function sendViaStandardMail(object $notifiable, Notification $notification): void
    {
        // Use the default mail channel
        $channel = new \Illuminate\Notifications\Channels\MailChannel(
            Mail::getFacadeRoot(),
            View::getFacadeRoot()
        );

        $channel->send($notifiable, $notification);
    }

    /**
     * Ensure mail views are registered to avoid namespace issues.
     */
    private function ensureMailViewsAreRegistered(): void
    {
        try {
            $viewFactory = View::getFacadeRoot();

            // Try to register mail namespace
            $mailViewsPath = base_path('vendor/laravel/framework/src/Illuminate/Mail/resources/views');
            if (is_dir($mailViewsPath)) {
                $viewFactory->addNamespace('mail', $mailViewsPath);
            }
        } catch (\Exception $e) {
            // If registration fails, we'll try to continue without it
            // The error will be caught at the mail sending level
        }
    }
}
