<?php

namespace App\Channels;

use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;

/**
 * Custom notification channel for sending emails via Resend mailer.
 * This channel ensures that all notifications use the Resend service.
 */
class ResendMailChannel
{
    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        if (! method_exists($notification, 'toMail')) {
            return;
        }

        $message = $notification->toMail($notifiable);

        // If the message is a Mailable, send it directly
        if ($message instanceof Mailable) {
            Mail::mailer('resend')->to($notifiable->routeNotificationFor('mail', $notification))->send($message);
            return;
        }

        if (! $message instanceof MailMessage) {
            return;
        }

        $to = $notifiable->routeNotificationFor('mail', $notification);

        if (! $to) {
            return;
        }

        // Build the mail using <PERSON><PERSON>'s mail system with Resend mailer
        Mail::mailer('resend')->send($message->markdown ?: $message->view, $message->viewData, function ($mail) use ($message, $to) {
            $mail->to($to)
                ->subject($message->subject);

            // Handle from address
            if ($message->from) {
                if (is_array($message->from)) {
                    $mail->from($message->from[0], $message->from[1] ?? null);
                } else {
                    $mail->from($message->from);
                }
            }

            // Handle reply-to
            if (! empty($message->replyTo)) {
                foreach ($message->replyTo as $replyTo) {
                    if (is_array($replyTo)) {
                        $mail->replyTo($replyTo[0], $replyTo[1] ?? null);
                    } else {
                        $mail->replyTo($replyTo);
                    }
                }
            }

            // Handle cc
            if (! empty($message->cc)) {
                foreach ($message->cc as $cc) {
                    if (is_array($cc)) {
                        $mail->cc($cc[0], $cc[1] ?? null);
                    } else {
                        $mail->cc($cc);
                    }
                }
            }

            // Handle bcc
            if (! empty($message->bcc)) {
                foreach ($message->bcc as $bcc) {
                    if (is_array($bcc)) {
                        $mail->bcc($bcc[0], $bcc[1] ?? null);
                    } else {
                        $mail->bcc($bcc);
                    }
                }
            }

            // Handle attachments
            foreach ($message->attachments as $attachment) {
                $mail->attach($attachment['file'], $attachment['options']);
            }

            // Handle raw attachments
            foreach ($message->rawAttachments as $attachment) {
                $mail->attachData($attachment['data'], $attachment['name'], $attachment['options']);
            }
        });
    }
}
