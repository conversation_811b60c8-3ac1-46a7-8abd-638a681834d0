<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for various rate limiting features
    | throughout the application. You can adjust these values based on your
    | application's requirements and environment.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Template Favorites Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting template favorite operations to prevent
    | abuse and ensure fair usage across all users.
    |
    */
    'template_favorites' => [
        'max_attempts' => env('RATE_LIMIT_TEMPLATE_FAVORITES_ATTEMPTS', 10),
        'decay_minutes' => env('RATE_LIMIT_TEMPLATE_FAVORITES_DECAY', 1),
        'key_prefix' => 'template_favorite',
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Verification Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting email verification requests to prevent
    | spam and abuse of the email verification system.
    |
    */
    'email_verification' => [
        'max_attempts' => env('RATE_LIMIT_EMAIL_VERIFICATION_ATTEMPTS', 6),
        'decay_minutes' => env('RATE_LIMIT_EMAIL_VERIFICATION_DECAY', 1),
        'key_prefix' => 'email_verification',
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Reset Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting password reset requests to prevent
    | abuse of the password reset functionality.
    |
    */
    'password_reset' => [
        'max_attempts' => env('RATE_LIMIT_PASSWORD_RESET_ATTEMPTS', 5),
        'decay_minutes' => env('RATE_LIMIT_PASSWORD_RESET_DECAY', 15),
        'key_prefix' => 'password_reset',
    ],

    /*
    |--------------------------------------------------------------------------
    | API Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for general API rate limiting to control the number of
    | requests users can make to the API endpoints.
    |
    */
    'api' => [
        'max_attempts' => env('RATE_LIMIT_API_ATTEMPTS', 60),
        'decay_minutes' => env('RATE_LIMIT_API_DECAY', 1),
        'key_prefix' => 'api',
    ],

    /*
    |--------------------------------------------------------------------------
    | Login Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting login attempts to prevent brute force
    | attacks on user accounts.
    |
    */
    'login' => [
        'max_attempts' => env('RATE_LIMIT_LOGIN_ATTEMPTS', 5),
        'decay_minutes' => env('RATE_LIMIT_LOGIN_DECAY', 15),
        'key_prefix' => 'login',
    ],

];
