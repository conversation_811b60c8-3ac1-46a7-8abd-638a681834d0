<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\URL;
use Tests\TestCase;

class CustomerEmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_customer_email_verification_with_valid_hash()
    {
        Event::fake();
        Notification::fake();

        $customer = Customer::factory()->create([
            'email_verified_at' => null,
        ]);

        $hash = sha1($customer->getEmailForVerification());
        $url = URL::temporarySignedRoute(
            'customer.verification.verify',
            now()->addMinutes(60),
            ['id' => $customer->id, 'hash' => $hash]
        );

        $response = $this->get($url);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('success');

        $customer->refresh();
        $this->assertNotNull($customer->email_verified_at);

        Event::assertDispatched(Verified::class);
    }

    public function test_customer_email_verification_with_invalid_hash()
    {
        $customer = Customer::factory()->create([
            'email_verified_at' => null,
        ]);

        $invalidHash = 'invalid-hash';
        $url = URL::temporarySignedRoute(
            'customer.verification.verify',
            now()->addMinutes(60),
            ['id' => $customer->id, 'hash' => $invalidHash]
        );

        $response = $this->get($url);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('error');

        $customer->refresh();
        $this->assertNull($customer->email_verified_at);
    }

    public function test_customer_email_verification_with_nonexistent_customer()
    {
        $nonExistentId = 99999;
        $hash = 'some-hash';
        
        $url = URL::temporarySignedRoute(
            'customer.verification.verify',
            now()->addMinutes(60),
            ['id' => $nonExistentId, 'hash' => $hash]
        );

        $response = $this->get($url);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('error', __('messages.email.customer_not_found'));
    }

    public function test_customer_email_verification_already_verified()
    {
        $customer = Customer::factory()->create([
            'email_verified_at' => now(),
        ]);

        $hash = sha1($customer->getEmailForVerification());
        $url = URL::temporarySignedRoute(
            'customer.verification.verify',
            now()->addMinutes(60),
            ['id' => $customer->id, 'hash' => $hash]
        );

        $response = $this->get($url);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('status');
    }

    public function test_customer_email_verification_resend()
    {
        Notification::fake();

        $customer = Customer::factory()->create([
            'email_verified_at' => null,
        ]);

        $customer->sendEmailVerificationNotification();

        Notification::assertSentTo(
            $customer,
            \App\Notifications\Customer\CustomerEmailVerificationNotification::class
        );
    }
}
