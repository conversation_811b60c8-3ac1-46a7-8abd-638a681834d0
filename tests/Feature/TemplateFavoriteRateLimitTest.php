<?php

namespace Tests\Feature;

use App\Models\CustomerUser;
use App\Models\Template;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\RateLimiter;
use Tests\TestCase;

class TemplateFavoriteRateLimitTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        RateLimiter::clear('template_favorite:user:1');
        RateLimiter::clear('template_favorite:ip:127.0.0.1');
    }

    public function test_template_favorite_rate_limiting_for_authenticated_user()
    {
        $user = CustomerUser::factory()->create();
        $template = Template::factory()->create();

        $this->actingAs($user, 'customer_user');

        // Get rate limit config
        $maxAttempts = config('rate_limiting.template_favorites.max_attempts');

        // Make requests up to the limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $response = $this->postJson(route('template-favorites.toggle', $template));
            $response->assertStatus(200);
        }

        // The next request should be rate limited
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(429);
        $response->assertJsonStructure([
            'message',
            'retry_after',
        ]);
    }

    public function test_template_favorite_rate_limiting_for_guest_user()
    {
        $template = Template::factory()->create();

        // Get rate limit config
        $maxAttempts = config('rate_limiting.template_favorites.max_attempts');

        // Make requests up to the limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $response = $this->postJson(route('template-favorites.toggle', $template));
            // Guest users might get 401, but rate limiting should still work
            $this->assertContains($response->status(), [200, 401]);
        }

        // The next request should be rate limited
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(429);
    }

    public function test_rate_limit_resets_after_decay_period()
    {
        $user = CustomerUser::factory()->create();
        $template = Template::factory()->create();

        $this->actingAs($user, 'customer_user');

        $maxAttempts = config('rate_limiting.template_favorites.max_attempts');

        // Exhaust the rate limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $this->postJson(route('template-favorites.toggle', $template));
        }

        // Verify rate limited
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(429);

        // Clear the rate limiter (simulating time passage)
        RateLimiter::clear('template_favorite:user:' . $user->id);

        // Should work again
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(200);
    }

    public function test_different_users_have_separate_rate_limits()
    {
        $user1 = CustomerUser::factory()->create();
        $user2 = CustomerUser::factory()->create();
        $template = Template::factory()->create();

        $maxAttempts = config('rate_limiting.template_favorites.max_attempts');

        // Exhaust rate limit for user1
        $this->actingAs($user1, 'customer_user');
        for ($i = 0; $i < $maxAttempts; $i++) {
            $this->postJson(route('template-favorites.toggle', $template));
        }

        // User1 should be rate limited
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(429);

        // User2 should not be affected
        $this->actingAs($user2, 'customer_user');
        $response = $this->postJson(route('template-favorites.toggle', $template));
        $response->assertStatus(200);
    }

    public function test_rate_limit_configuration_is_loaded_correctly()
    {
        $config = config('rate_limiting.template_favorites');

        $this->assertIsArray($config);
        $this->assertArrayHasKey('max_attempts', $config);
        $this->assertArrayHasKey('decay_minutes', $config);
        $this->assertArrayHasKey('key_prefix', $config);
        
        $this->assertIsInt($config['max_attempts']);
        $this->assertIsInt($config['decay_minutes']);
        $this->assertIsString($config['key_prefix']);
    }
}
