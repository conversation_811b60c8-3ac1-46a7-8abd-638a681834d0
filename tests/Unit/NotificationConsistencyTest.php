<?php

namespace Tests\Unit;

use App\Models\Customer;
use App\Models\CustomerUser;
use App\Notifications\Auth\AuthEmailChangedNotification;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use App\Notifications\Auth\AuthPasswordChangedNotification;
use App\Notifications\Customer\CustomerEmailChangedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Notifications\Messages\MailMessage;
use Tests\TestCase;

class NotificationConsistencyTest extends TestCase
{
    use RefreshDatabase;

    public function test_auth_email_changed_notification_structure()
    {
        $user = CustomerUser::factory()->make();
        $oldEmail = '<EMAIL>';
        
        $notification = new AuthEmailChangedNotification($user, $oldEmail);
        
        $this->assertEquals(['mail'], $notification->via(new \stdClass()));
        
        $mailMessage = $notification->toMail(new \stdClass());
        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        
        $arrayData = $notification->toArray(new \stdClass());
        $this->assertArrayHasKey('user_id', $arrayData);
        $this->assertArrayHasKey('old_email', $arrayData);
        $this->assertArrayHasKey('new_email', $arrayData);
        $this->assertArrayHasKey('changed_at', $arrayData);
    }

    public function test_customer_email_changed_notification_structure()
    {
        $customer = Customer::factory()->make();
        $oldEmail = '<EMAIL>';
        
        $notification = new CustomerEmailChangedNotification($customer, $oldEmail);
        
        $this->assertEquals(['mail'], $notification->via(new \stdClass()));
        
        $mailMessage = $notification->toMail(new \stdClass());
        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        
        $arrayData = $notification->toArray(new \stdClass());
        $this->assertArrayHasKey('customer_id', $arrayData);
        $this->assertArrayHasKey('old_email', $arrayData);
        $this->assertArrayHasKey('new_email', $arrayData);
        $this->assertArrayHasKey('changed_at', $arrayData);
    }

    public function test_auth_password_changed_notification_structure()
    {
        $user = CustomerUser::factory()->make();
        
        $notification = new AuthPasswordChangedNotification($user);
        
        $this->assertEquals(['mail'], $notification->via(new \stdClass()));
        
        $mailMessage = $notification->toMail(new \stdClass());
        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        
        $arrayData = $notification->toArray(new \stdClass());
        $this->assertArrayHasKey('user_id', $arrayData);
        $this->assertArrayHasKey('changed_at', $arrayData);
    }

    public function test_auth_email_verified_notification_structure()
    {
        $user = CustomerUser::factory()->make();
        
        $notification = new AuthEmailVerifiedNotification($user);
        
        $this->assertEquals(['mail'], $notification->via(new \stdClass()));
        
        $mailMessage = $notification->toMail(new \stdClass());
        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        
        $arrayData = $notification->toArray(new \stdClass());
        $this->assertArrayHasKey('user_id', $arrayData);
        $this->assertArrayHasKey('verified_at', $arrayData);
    }

    public function test_notification_naming_consistency()
    {
        // Test that notification class names follow consistent patterns
        $authNotifications = [
            AuthEmailChangedNotification::class,
            AuthPasswordChangedNotification::class,
            AuthEmailVerifiedNotification::class,
        ];

        $customerNotifications = [
            CustomerEmailChangedNotification::class,
        ];

        // All Auth notifications should start with "Auth"
        foreach ($authNotifications as $notificationClass) {
            $className = class_basename($notificationClass);
            $this->assertStringStartsWith('Auth', $className);
        }

        // All Customer notifications should start with "Customer"
        foreach ($customerNotifications as $notificationClass) {
            $className = class_basename($notificationClass);
            $this->assertStringStartsWith('Customer', $className);
        }
    }

    public function test_all_notifications_implement_should_queue()
    {
        $notifications = [
            AuthEmailChangedNotification::class,
            AuthPasswordChangedNotification::class,
            AuthEmailVerifiedNotification::class,
            CustomerEmailChangedNotification::class,
        ];

        foreach ($notifications as $notificationClass) {
            $reflection = new \ReflectionClass($notificationClass);
            $interfaces = $reflection->getInterfaceNames();
            
            $this->assertContains(
                'Illuminate\Contracts\Queue\ShouldQueue',
                $interfaces,
                "Notification {$notificationClass} should implement ShouldQueue"
            );
        }
    }

    public function test_notification_translation_keys_exist()
    {
        // Test that required translation keys exist
        $requiredKeys = [
            'messages.email.email_changed_subject',
            'messages.email.customer_email_changed_subject',
            'messages.email.password_changed_subject',
            'messages.emails.email_verified.title',
        ];

        foreach ($requiredKeys as $key) {
            $translation = __($key);
            $this->assertNotEquals($key, $translation, "Translation key {$key} is missing");
        }
    }
}
