<?php

namespace Tests\Unit;

use App\Models\Customer;
use App\Models\CustomerUser;
use App\Notifications\Auth\AuthEmailChangedNotification;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use App\Notifications\Auth\AuthPasswordChangedNotification;
use App\Notifications\Customer\CustomerEmailChangedNotification;
use App\Services\NotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected NotificationService $notificationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->notificationService = new NotificationService();
    }

    public function test_send_user_email_changed_notification()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();
        $oldEmail = '<EMAIL>';

        $this->notificationService->sendUserEmailChangedNotification($user, $oldEmail);

        Notification::assertSentTo(
            Notification::route('mail', $oldEmail),
            AuthEmailChangedNotification::class,
            function ($notification) use ($user, $oldEmail) {
                return $notification->user->id === $user->id 
                    && $notification->oldEmail === $oldEmail;
            }
        );
    }

    public function test_send_user_password_changed_notification()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();

        $this->notificationService->sendUserPasswordChangedNotification($user);

        Notification::assertSentTo(
            $user,
            AuthPasswordChangedNotification::class,
            function ($notification) use ($user) {
                return $notification->user->id === $user->id;
            }
        );
    }

    public function test_send_user_email_verified_notification()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();

        $this->notificationService->sendUserEmailVerifiedNotification($user);

        Notification::assertSentTo(
            $user,
            AuthEmailVerifiedNotification::class,
            function ($notification) use ($user) {
                return $notification->user->id === $user->id;
            }
        );
    }

    public function test_send_customer_email_changed_notification()
    {
        Notification::fake();

        $customer = Customer::factory()->create();
        $oldEmail = '<EMAIL>';

        $this->notificationService->sendCustomerEmailChangedNotification($customer, $oldEmail);

        Notification::assertSentTo(
            Notification::route('mail', $oldEmail),
            CustomerEmailChangedNotification::class,
            function ($notification) use ($customer, $oldEmail) {
                return $notification->customer->id === $customer->id 
                    && $notification->oldEmail === $oldEmail;
            }
        );
    }

    public function test_send_customer_email_verified_notification()
    {
        Notification::fake();

        $customer = Customer::factory()->create();

        $this->notificationService->sendCustomerEmailVerifiedNotification($customer);

        Notification::assertSentTo(
            $customer,
            AuthEmailVerifiedNotification::class,
            function ($notification) use ($customer) {
                return $notification->user->id === $customer->id;
            }
        );
    }

    public function test_get_available_notifications_returns_correct_structure()
    {
        $notifications = $this->notificationService->getAvailableNotifications();

        $this->assertIsArray($notifications);
        $this->assertNotEmpty($notifications);

        foreach ($notifications as $key => $config) {
            $this->assertIsString($key);
            $this->assertArrayHasKey('class', $config);
            $this->assertArrayHasKey('description', $config);
            $this->assertArrayHasKey('target', $config);
            
            $this->assertIsString($config['class']);
            $this->assertIsString($config['description']);
            $this->assertIsString($config['target']);
        }
    }

    public function test_validate_notification_consistency_with_valid_notifications()
    {
        $result = $this->notificationService->validateNotificationConsistency();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('total_notifications', $result);
        $this->assertArrayHasKey('issues', $result);
        $this->assertArrayHasKey('is_consistent', $result);

        $this->assertIsInt($result['total_notifications']);
        $this->assertIsArray($result['issues']);
        $this->assertIsBool($result['is_consistent']);

        $this->assertGreaterThan(0, $result['total_notifications']);
    }

    public function test_notification_service_handles_all_expected_notification_types()
    {
        $expectedNotifications = [
            'user_email_changed',
            'user_password_changed',
            'user_email_verified',
            'customer_email_changed',
            'customer_email_verified',
        ];

        $availableNotifications = $this->notificationService->getAvailableNotifications();

        foreach ($expectedNotifications as $expectedType) {
            $this->assertArrayHasKey(
                $expectedType, 
                $availableNotifications,
                "Missing notification type: {$expectedType}"
            );
        }
    }

    public function test_all_notification_classes_exist()
    {
        $notifications = $this->notificationService->getAvailableNotifications();

        foreach ($notifications as $key => $config) {
            $this->assertTrue(
                class_exists($config['class']),
                "Notification class {$config['class']} does not exist"
            );
        }
    }

    public function test_all_notifications_implement_should_queue()
    {
        $notifications = $this->notificationService->getAvailableNotifications();

        foreach ($notifications as $key => $config) {
            $reflection = new \ReflectionClass($config['class']);
            $interfaces = $reflection->getInterfaceNames();
            
            $this->assertContains(
                'Illuminate\Contracts\Queue\ShouldQueue',
                $interfaces,
                "Notification {$config['class']} should implement ShouldQueue"
            );
        }
    }
}
