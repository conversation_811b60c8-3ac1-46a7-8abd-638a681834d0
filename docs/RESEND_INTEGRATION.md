# Tích hợp Resend Mail Service

## Tổng quan

Dự án đã được tích hợp với Resend mail service để cải thiện khả năng gửi email và độ tin cậy. Tất cả các notification hiện tại đã được cập nhật để sử dụng Resend thay vì mail driver mặc định.

## Cấu hình

### 1. Cài đặt biến môi trường

Thêm vào file `.env`:

```env
MAIL_MAILER=resend
RESEND_API_KEY=your_resend_api_key_here
```

### 2. <PERSON><PERSON><PERSON> hình mail

File `config/mail.php` đã được cập nhật để sử dụng `resend` làm mailer mặc định.

## Các thành phần đã được tạo

### 1. ResendMailChannel (`app/Channels/ResendMailChannel.php`)

Custom notification channel để gửi email qua Resend service.

### 2. ResendMailService (`app/Services/ResendMailService.php`)

Service class cung cấp các phương thức để gửi email qua Resend:

- `sendNotification()` - Gửi notification qua Resend
- `sendMail()` - Gửi email trực tiếp
- `sendMarkdownMail()` - Gửi markdown email

### 3. SendsNotificationsViaResend Trait (`app/Traits/SendsNotificationsViaResend.php`)

Trait để các model có thể dễ dàng gửi notification qua Resend:

- `notifyViaResend()` - Gửi notification qua Resend
- `sendEmailVerificationNotificationViaResend()` - Gửi email verification qua Resend
- `sendPasswordResetNotificationViaResend()` - Gửi password reset qua Resend

### 4. NotificationServiceProvider (`app/Providers/NotificationServiceProvider.php`)

Service provider để đăng ký ResendMailChannel.

## Các notification đã được cập nhật

Tất cả các notification class đã được cập nhật để sử dụng `ResendMailChannel`:

- `VerifyEmailNotification`
- `CustomerEmailVerificationNotification`
- `AuthEmailVerifiedNotification`
- `ResetPasswordNotification`

## Các model đã được cập nhật

- `Customer` - Đã thêm `SendsNotificationsViaResend` trait
- `CustomerUser` - Đã thêm `SendsNotificationsViaResend` trait

## Các controller đã được cập nhật

- `VerifyCustomerEmailController` - Sử dụng Resend với fallback
- `EmailVerificationController` - Sử dụng Resend với fallback

## Cách sử dụng

### 1. Gửi notification thông thường

```php
// Notification sẽ tự động sử dụng ResendMailChannel
$user->notify(new VerifyEmailNotification());
```

### 2. Gửi notification qua Resend trực tiếp

```php
// Sử dụng trait method
$user->notifyViaResend(new VerifyEmailNotification());

// Hoặc sử dụng service
$resendService = app(ResendMailService::class);
$resendService->sendNotification($user, new VerifyEmailNotification());
```

### 3. Gửi email verification qua Resend

```php
// Sử dụng method mới với fallback
$user->sendEmailVerificationNotificationViaResend();
```

### 4. Gửi email trực tiếp

```php
$resendService = app(ResendMailService::class);

// Gửi email thông thường
$resendService->sendMail(
    '<EMAIL>',
    'Subject',
    'emails.template',
    ['data' => 'value']
);

// Gửi markdown email
$resendService->sendMarkdownMail(
    '<EMAIL>',
    'Subject',
    'emails.markdown-template',
    ['data' => 'value']
);
```

## Testing

### Command để test

```bash
# Test với customer
php artisan test:resend-email --type=customer --email=<EMAIL>

# Test với customer user
php artisan test:resend-email --type=customer_user --id=1

# Test với user đầu tiên trong database
php artisan test:resend-email
```

### Kiểm tra logs

Tất cả các hoạt động gửi email qua Resend đều được log trong `storage/logs/laravel.log`.

## Fallback Strategy

Hệ thống được thiết kế với fallback strategy:

1. **Ưu tiên**: Gửi qua Resend
2. **Fallback**: Nếu Resend thất bại, sử dụng notification system mặc định
3. **Logging**: Tất cả lỗi đều được log để debug

## Lưu ý quan trọng

1. **API Key**: Đảm bảo có API key hợp lệ từ Resend
2. **Domain verification**: Domain gửi email cần được verify trong Resend dashboard
3. **Rate limiting**: Chú ý rate limit của Resend service
4. **Monitoring**: Theo dõi logs để đảm bảo email được gửi thành công

## Troubleshooting

### Email không được gửi

1. Kiểm tra API key trong `.env`
2. Kiểm tra domain đã được verify
3. Xem logs trong `storage/logs/laravel.log`
4. Test với command `php artisan test:resend-email`

### Fallback không hoạt động

1. Kiểm tra cấu hình mail backup trong `config/mail.php`
2. Đảm bảo queue worker đang chạy nếu sử dụng queue
3. Kiểm tra notification class có implement đúng interface

## Migration từ Mail Facade

Nếu trước đây bạn sử dụng Mail Facade:

```php
// Cũ
Mail::to($user)->send(new WelcomeMail());

// Mới - sử dụng notification
$user->notify(new WelcomeNotification());

// Hoặc sử dụng ResendMailService
$resendService->sendMail($user->email, 'Welcome', 'emails.welcome', ['user' => $user]);
```
